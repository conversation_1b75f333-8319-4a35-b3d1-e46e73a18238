#!/bin/bash

echo "🚀 Building Lcheck v3.2.0 for Windows (Fyne Package Build)..."

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Error: Go not found. Please install Go first."
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
echo "📋 Go version: $GO_VERSION"

echo "📦 Downloading dependencies..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "❌ Failed to download dependencies!"
    exit 1
fi

echo "� Installing Fyne command line tool..."
go install fyne.io/tools/cmd/fyne@latest

echo "🔨 Setting up cross-compilation environment..."

# 安装交叉编译工具
if command -v apt-get &> /dev/null; then
    echo "📥 Installing MinGW-w64 on Ubuntu/Debian..."
    sudo apt-get update && sudo apt-get install -y gcc-mingw-w64-x86-64 gcc-mingw-w64-i686
elif command -v yum &> /dev/null; then
    echo "📥 Installing MinGW-w64 on CentOS/RHEL..."
    sudo yum install -y mingw64-gcc mingw32-gcc
elif command -v brew &> /dev/null; then
    echo "📥 Installing MinGW-w64 on macOS..."
    brew install mingw-w64
else
    echo "⚠️  Please install MinGW-w64 manually for your system"
fi

# 创建输出目录
mkdir -p dist

echo "🏗️  Building Windows 64-bit version..."
# Windows 64位版本 - 静态链接，最大兼容性
CGO_ENABLED=1 \
GOOS=windows \
GOARCH=amd64 \
CC=x86_64-w64-mingw32-gcc \
CGO_LDFLAGS="-static -static-libgcc -static-libstdc++" \
CGO_CFLAGS="-D_WIN32_WINNT=0x0601" \
fyne package -os windows -name "Lcheck-fyne-x64"

echo "🏗️  Building Windows 32-bit version..."
# Windows 32位版本 - 兼容老系统
CGO_ENABLED=1 \
GOOS=windows \
GOARCH=386 \
CC=i686-w64-mingw32-gcc \
CGO_LDFLAGS="-static -static-libgcc -static-libstdc++" \
CGO_CFLAGS="-D_WIN32_WINNT=0x0601" \
fyne package -os windows -name "Lcheck-fyne-x86"

# 检查构建结果
echo "🔍 Checking build results..."

BUILD_SUCCESS=false

# 检查64位版本
if [ -f "Lcheck-fyne-x64.exe" ]; then
    echo "✅ Windows 64-bit build successful!"
    echo "📁 Executable: Lcheck-fyne-x64.exe"
    echo "📊 File size: $(du -h 'Lcheck-fyne-x64.exe' | cut -f1)"
    mv "Lcheck-fyne-x64.exe" "dist/"
    BUILD_SUCCESS=true
fi

# 检查32位版本
if [ -f "Lcheck-fyne-x86.exe" ]; then
    echo "✅ Windows 32-bit build successful!"
    echo "📁 Executable: Lcheck-fyne-x86.exe"
    echo "📊 File size: $(du -h 'Lcheck-fyne-x86.exe' | cut -f1)"
    mv "Lcheck-fyne-x86.exe" "dist/"
    BUILD_SUCCESS=true
fi

# 检查其他可能的文件名
for file in *.exe; do
    if [ -f "$file" ] && [ "$file" != "Lcheck-fyne-x64.exe" ] && [ "$file" != "Lcheck-fyne-x86.exe" ]; then
        echo "✅ Found additional executable: $file"
        echo "📊 File size: $(du -h "$file" | cut -f1)"
        mv "$file" "dist/"
        BUILD_SUCCESS=true
    fi
done

if [ "$BUILD_SUCCESS" = true ]; then
    echo ""
    echo "🎯 Build completed successfully!"
    echo "📁 All executables moved to dist/ directory"
    echo ""
    echo "🔧 Features included:"
    echo "   ✅ Enhanced Windows compatibility (supports Windows 7+)"
    echo "   ✅ Static linking for maximum portability"
    echo "   ✅ Both 32-bit and 64-bit versions"
    echo "   ✅ Modular architecture with clean separation"
    echo "   ✅ SSH connection management"
    echo "   ✅ Comprehensive baseline security checks (12+ items)"
    echo "   ✅ Host and host group management"
    echo "   ✅ Batch scanning with progress tracking"
    echo "   ✅ Data persistence and export"
    echo "   ✅ Native Fyne GUI with professional look"
    echo ""
    echo "🚀 Deployment instructions:"
    echo "   📋 For maximum compatibility:"
    echo "      - Use x64 version for 64-bit Windows systems"
    echo "      - Use x86 version for 32-bit Windows or older systems"
    echo "   📋 Installation:"
    echo "      1. Copy the appropriate .exe file to Windows machine"
    echo "      2. No additional dependencies required"
    echo "      3. Double-click to run"
    echo "      4. Data will be saved in the same directory as executable"
    echo ""
    echo "🛡️  Compatibility:"
    echo "   ✅ Windows 7 SP1 and later"
    echo "   ✅ Windows Server 2008 R2 and later"
    echo "   ✅ Both 32-bit and 64-bit architectures"
    echo "   ✅ No external runtime dependencies"
    echo ""
    echo "📚 Project structure:"
    echo "   - core/: SSH, baseline checks, scanner engine"
    echo "   - data/: Models and storage management"
    echo "   - ui/: Fyne GUI components"
    echo "   - utils/: Helper functions"
    echo "   - config/: Configuration management"
else
    echo "❌ Windows build failed - no executables found!"
    echo "🔧 Troubleshooting tips:"
    echo "   1. Ensure MinGW-w64 is properly installed"
    echo "   2. Check Go version (requires Go 1.19+)"
    echo "   3. Verify Fyne tools installation"
    echo "   4. Check for CGO compilation errors above"
    exit 1
fi
