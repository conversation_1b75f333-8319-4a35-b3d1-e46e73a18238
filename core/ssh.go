package core

import (
	"fmt"
	"net"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
	"lcheck/data"
)

// SSHClient SSH客户端管理器
type SSHClient struct {
	config  *ssh.ClientConfig
	timeout time.Duration
}

// NewSSHClient 创建SSH客户端
func NewSSHClient(host data.HostInfo, timeout time.Duration) *SSHClient {
	config := &ssh.ClientConfig{
		User: host.Username,
		Auth: []ssh.AuthMethod{
			ssh.Password(host.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         timeout,
	}

	return &SSHClient{
		config:  config,
		timeout: timeout,
	}
}

// TestConnection 测试SSH连接
func (c *SSHClient) TestConnection(host data.HostInfo) error {
	addr := fmt.Sprintf("%s:%s", host.Host, host.Port)

	// 先测试TCP连接
	conn, err := net.DialTimeout("tcp", addr, c.timeout)
	if err != nil {
		return fmt.Errorf("TCP连接失败: %v", err)
	}
	conn.Close()

	// 测试SSH连接
	client, err := ssh.Dial("tcp", addr, c.config)
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer client.Close()

	// 测试执行简单命令
	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	_, err = session.Output("echo 'connection test'")
	if err != nil {
		return fmt.Errorf("执行测试命令失败: %v", err)
	}

	return nil
}

// ExecuteCommand 执行单个命令
func (c *SSHClient) ExecuteCommand(host data.HostInfo, command string) (string, error) {
	addr := fmt.Sprintf("%s:%s", host.Host, host.Port)

	client, err := ssh.Dial("tcp", addr, c.config)
	if err != nil {
		return "", fmt.Errorf("SSH连接失败: %v", err)
	}
	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	output, err := session.Output(command)
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %v", err)
	}

	return strings.TrimSpace(string(output)), nil
}

// ExecuteCommands 执行多个命令
func (c *SSHClient) ExecuteCommands(host data.HostInfo, commands map[string]string) (map[string]string, error) {
	results := make(map[string]string)

	for key, command := range commands {
		output, err := c.ExecuteCommand(host, command)
		if err != nil {
			results[key] = fmt.Sprintf("ERROR: %v", err)
		} else {
			results[key] = output
		}
	}

	return results, nil
}

// GetSystemInfo 获取系统信息
func (c *SSHClient) GetSystemInfo(host data.HostInfo) (*data.SystemInfo, error) {
	commands := map[string]string{
		"hostname":     "hostname",
		"os":           "cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'",
		"osVersion":    "cat /etc/os-release | grep VERSION_ID | cut -d'=' -f2 | tr -d '\"'",
		"kernel":       "uname -r",
		"architecture": "uname -m",
		"uptime":       "uptime -p",
		"cpuInfo":      "lscpu | grep 'Model name' | cut -d':' -f2 | xargs",
		"cpuCores":     "nproc",
		"memoryTotal":  "free -h | grep Mem | awk '{print $2}'",
		"memoryUsed":   "free -h | grep Mem | awk '{print $3}'",
		"diskInfo":     "df -h",
		"networkInfo":  "ip addr show",
		"processes":    "ps aux | wc -l",
		"loadAverage":  "uptime | awk -F'load average:' '{print $2}'",
		"users":        "cat /etc/passwd | grep -E '/bin/(bash|sh)$'",
		"services":     "systemctl list-units --type=service --state=running | grep -v LOAD",
	}

	results, err := c.ExecuteCommands(host, commands)
	if err != nil {
		return nil, err
	}

	systemInfo := &data.SystemInfo{
		Hostname:     results["hostname"],
		OS:           results["os"],
		OSVersion:    results["osVersion"],
		Kernel:       results["kernel"],
		Architecture: results["architecture"],
		Uptime:       results["uptime"],
		CPUInfo:      results["cpuInfo"],
		MemoryTotal:  results["memoryTotal"],
		MemoryUsed:   results["memoryUsed"],
		LoadAverage:  results["loadAverage"],
		CollectedAt:  time.Now(),
		Environment:  make(map[string]string),
	}

	// 解析CPU核心数
	if cpuCores := results["cpuCores"]; cpuCores != "" {
		if cores, err := parseIntFromString(cpuCores); err == nil {
			systemInfo.CPUCores = cores
		}
	}

	// 解析进程数
	if processes := results["processes"]; processes != "" {
		if proc, err := parseIntFromString(processes); err == nil {
			systemInfo.Processes = proc
		}
	}

	// 解析磁盘信息
	systemInfo.DiskInfo = parseDiskInfo(results["diskInfo"])

	// 解析网络信息
	systemInfo.NetworkInfo = parseNetworkInfo(results["networkInfo"])

	// 解析用户信息
	systemInfo.Users = parseUserInfo(results["users"])

	// 解析服务信息
	systemInfo.Services = parseServiceInfo(results["services"])

	return systemInfo, nil
}

// 辅助函数
func parseIntFromString(s string) (int, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, fmt.Errorf("empty string")
	}

	var result int
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
}

func parseDiskInfo(output string) []data.DiskInfo {
	var diskInfo []data.DiskInfo
	lines := strings.Split(output, "\n")

	for i, line := range lines {
		if i == 0 || strings.TrimSpace(line) == "" {
			continue // 跳过标题行和空行
		}

		fields := strings.Fields(line)
		if len(fields) >= 6 {
			diskInfo = append(diskInfo, data.DiskInfo{
				Device:     fields[0],
				Size:       fields[1],
				Used:       fields[2],
				Available:  fields[3],
				UsePercent: fields[4],
				MountPoint: fields[5],
			})
		}
	}

	return diskInfo
}

func parseNetworkInfo(output string) []data.NetworkInfo {
	var networkInfo []data.NetworkInfo
	lines := strings.Split(output, "\n")

	var currentInterface string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, ":") && !strings.Contains(line, "inet") {
			// 接口名称行
			parts := strings.Split(line, ":")
			if len(parts) > 0 {
				currentInterface = strings.TrimSpace(parts[0])
			}
		} else if strings.Contains(line, "inet ") && currentInterface != "" {
			// IP地址行
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				ipAddr := fields[1]
				if strings.Contains(ipAddr, "/") {
					ipAddr = strings.Split(ipAddr, "/")[0]
				}

				networkInfo = append(networkInfo, data.NetworkInfo{
					Interface: currentInterface,
					IPAddress: ipAddr,
					Status:    "UP",
				})
			}
		}
	}

	return networkInfo
}

func parseUserInfo(output string) []data.UserInfo {
	var users []data.UserInfo
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		fields := strings.Split(line, ":")
		if len(fields) >= 7 {
			users = append(users, data.UserInfo{
				Username: fields[0],
				UID:      fields[2],
				GID:      fields[3],
				Home:     fields[5],
				Shell:    fields[6],
			})
		}
	}

	return users
}

func parseServiceInfo(output string) []data.ServiceInfo {
	var services []data.ServiceInfo
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		if strings.TrimSpace(line) == "" || strings.Contains(line, "UNIT") {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) >= 4 {
			serviceName := fields[0]
			if strings.HasSuffix(serviceName, ".service") {
				serviceName = strings.TrimSuffix(serviceName, ".service")
			}

			services = append(services, data.ServiceInfo{
				Name:   serviceName,
				Status: fields[3],
			})
		}
	}

	return services
}

// FileExists 检查文件是否存在
func (c *SSHClient) FileExists(host data.HostInfo, filePath string) (bool, error) {
	command := fmt.Sprintf("test -f %s && echo 'exists' || echo 'not exists'", filePath)
	output, err := c.ExecuteCommand(host, command)
	if err != nil {
		return false, err
	}

	return strings.TrimSpace(output) == "exists", nil
}

// DirectoryExists 检查目录是否存在
func (c *SSHClient) DirectoryExists(host data.HostInfo, dirPath string) (bool, error) {
	command := fmt.Sprintf("test -d %s && echo 'exists' || echo 'not exists'", dirPath)
	output, err := c.ExecuteCommand(host, command)
	if err != nil {
		return false, err
	}

	return strings.TrimSpace(output) == "exists", nil
}

// GetFileContent 获取文件内容
func (c *SSHClient) GetFileContent(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("cat %s", filePath)
	return c.ExecuteCommand(host, command)
}

// GetFilePermissions 获取文件权限
func (c *SSHClient) GetFilePermissions(host data.HostInfo, filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%a' %s", filePath)
	return c.ExecuteCommand(host, command)
}
